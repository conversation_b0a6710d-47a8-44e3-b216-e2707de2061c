package com.wexl.erp.fees.service;

import static com.wexl.retail.commons.util.DateTimeUtil.convertIso8601ToEpoch;

import com.wexl.erp.fees.dto.FeeDto;
import com.wexl.erp.fees.events.FeeMasterEventPublisher;
import com.wexl.erp.fees.model.*;
import com.wexl.erp.fees.repository.*;
import com.wexl.retail.auth.AuthService;
import com.wexl.retail.commons.errorcodes.InternalErrorCodes;
import com.wexl.retail.commons.exceptions.ApiException;
import com.wexl.retail.commons.util.DateTimeUtil;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class FeeService {

  private final FeeTypeRepository feeTypeRepository;
  private final FeeGroupRepository feeGroupRepository;
  private final AuthService authService;
  private final FeeGroupFeeTypeRepository feeGroupFeeTypeRepository;
  private final DateTimeUtil dateTimeUtil;
  private final FeeHeadRepository feeHeadRepository;
  private final FeeMasterRepository feeMasterRepository;
  private final FeeMasterEventPublisher feeMasterEventPublisher;

  public void saveFeeTypes(String orgSlug, FeeDto.FeeTypeRequest request) {
    feeTypeRepository.save(
        FeeType.builder()
            .name(request.name())
            .code(request.code())
            .description(request.description())
            .orgSlug(orgSlug)
            .createdBy(authService.getUserDetails())
            .build());
  }

  public List<FeeDto.FeeTypeResponse> getFeeTypes(String orgSlug) {
    List<FeeDto.FeeTypeResponse> responses = new ArrayList<>();
    var feeTypes = feeTypeRepository.findAllByOrgSlug(orgSlug);
    if (feeTypes.isEmpty()) {
      return responses;
    }
    feeTypes.forEach(
        feeType ->
            responses.add(
                FeeDto.FeeTypeResponse.builder()
                    .id(feeType.getId())
                    .userId(feeType.getCreatedBy().getId())
                    .createdOn(convertIso8601ToEpoch(feeType.getCreatedAt().toLocalDateTime()))
                    .name(feeType.getName())
                    .code(feeType.getCode())
                    .description(feeType.getDescription())
                    .createdBy(
                        feeType.getCreatedBy().getFirstName()
                            + " "
                            + feeType.getCreatedBy().getLastName())
                    .build()));

    return responses;
  }

  public void updateFeeTypeById(String orgSlug, String feeTypeId, FeeDto.FeeTypeRequest request) {
    var feeType = getFeeTypeById(feeTypeId, orgSlug);
    feeType.setCode(request.code());
    feeType.setDescription(request.description());
    feeType.setName(request.name());
    feeTypeRepository.save(feeType);
  }

  private FeeType getFeeTypeById(String feeTypeId, String orgSlug) {
    var feeType = feeTypeRepository.findByIdAndOrgSlug(UUID.fromString(feeTypeId), orgSlug);
    if (feeType.isEmpty()) {
      throw new IllegalArgumentException("Fee type not found with id: " + feeTypeId);
    }
    return feeType.get();
  }

  public void deleteFeeTypeById(String orgSlug, String feeTypeId) {
    var feeType = getFeeTypeById(feeTypeId, orgSlug);
    try {
      feeTypeRepository.delete(feeType);
    } catch (Exception ex) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          String.format(
              "Cannot delete. This Fee Type %s is already mapped to one or more Fee Groups and cannot be removed.",
              feeTypeId));
    }
  }

  public FeeGroup saveFeeGroup(String orgSlug, FeeDto.FeeGroupRequest request) {
    return feeGroupRepository.save(
        FeeGroup.builder()
            .name(request.name())
            .description(request.description())
            .orgSlug(orgSlug)
            .createdBy(authService.getUserDetails())
            .isActive(Boolean.TRUE)
            .build());
  }

  public List<FeeDto.FeeGroupResponse> getFeeGroup(String orgSlug) {
    List<FeeDto.FeeGroupResponse> responses = new ArrayList<>();
    var feeGroups = feeGroupRepository.findAllByOrgSlug(orgSlug);
    if (feeGroups.isEmpty()) {
      return responses;
    }
    feeGroups.forEach(
        feeGroup ->
            responses.add(
                FeeDto.FeeGroupResponse.builder()
                    .id(feeGroup.getId())
                    .userId(feeGroup.getCreatedBy().getId())
                    .createdOn(convertIso8601ToEpoch(feeGroup.getCreatedAt().toLocalDateTime()))
                    .name(feeGroup.getName())
                    .description(feeGroup.getDescription())
                    .publishedAt(
                        feeGroup.getPublishedAt() == null
                            ? null
                            : convertIso8601ToEpoch(feeGroup.getPublishedAt()))
                    .createdBy(
                        feeGroup.getCreatedBy().getFirstName()
                            + " "
                            + feeGroup.getCreatedBy().getLastName())
                    .build()));

    return responses;
  }

  public FeeGroup getFeeGroupById(String feeGroupId, String orgSlug) {
    var feeType = feeGroupRepository.findByIdAndOrgSlug(UUID.fromString(feeGroupId), orgSlug);
    if (feeType.isEmpty()) {
      throw new IllegalArgumentException("Fee group not found with id: " + feeGroupId);
    }
    return feeType.get();
  }

  public void updateFeeGroupById(
      String orgSlug, String feeGroupId, FeeDto.FeeGroupRequest request) {
    var feeType = getFeeGroupById(feeGroupId, orgSlug);
    feeType.setDescription(request.description());
    feeType.setName(request.name());
    feeGroupRepository.save(feeType);
  }

  public void deleteFeeGroupById(String orgSlug, String feeGroupId) {
    var feeGroup = getFeeGroupById(feeGroupId, orgSlug);
    try {
      feeGroupRepository.delete(feeGroup);
    } catch (Exception ex) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST,
          String.format(
              "Cannot delete. This Fee Group %s is already mapped to one or more Fee Masters and cannot be removed. ",
              feeGroupId));
    }
  }

  public void saveFeeGroupFeeTypes(
      String feeGroupId, String orgSlug, FeeDto.FeeGroupFeeTypeRequest request) {
    var getFeeGroup = getFeeGroupById(feeGroupId, orgSlug);
    request
        .feeTypes()
        .forEach(
            type -> {
              var feeType = getFeeTypeById(type.feeTypeId(), orgSlug);
              var isExists =
                  feeGroupFeeTypeRepository.findByFeeGroupIdAndFeeTypeIdAndOrgSlug(
                      UUID.fromString(feeGroupId), UUID.fromString(type.feeTypeId()), orgSlug);

              if (isExists.isEmpty()) {
                feeGroupFeeTypeRepository.save(
                    FeeGroupFeeType.builder()
                        .feeGroup(getFeeGroup)
                        .feeType(feeType)
                        .remainderDays(type.remainderDays())
                        .amount(type.amount())
                        .dueDate(dateTimeUtil.convertEpochToIso8601Legacy(type.dueDate()))
                        .fineAmount(type.fineAmount())
                        .orgSlug(orgSlug)
                        .fineType(type.fineType())
                        .remainderDays(type.remainderDays())
                        .createdBy(authService.getUserDetails())
                        .build());
              }
            });
  }

  public List<FeeDto.FeeGroupFeeTypeResponse> getFeeGroupFeeTypes(
      String feeGroupId, String orgSlug) {
    List<FeeDto.FeeGroupFeeTypeResponse> responses = new ArrayList<>();
    var feeGroup = getFeeGroupById(feeGroupId, orgSlug);
    responses.add(buildFeeGroupFeeTypeResponse(feeGroup, orgSlug));
    return responses;
  }

  private FeeDto.FeeGroupFeeTypeResponse buildFeeGroupFeeTypeResponse(
      FeeGroup feeGroup, String orgSlug) {
    var feeGroupFeeTypes =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndOrgSlug(feeGroup.getId(), orgSlug);
    var feeTypes =
        feeGroupFeeTypes.stream()
            .filter(feeGroupFeeType -> feeGroupFeeType.getFeeGroup().equals(feeGroup))
            .toList();

    return FeeDto.FeeGroupFeeTypeResponse.builder()
        .feeGroupId(feeGroup.getId().toString())
        .name(feeGroup.getName())
        .description(feeGroup.getDescription())
        .feeTypes(buildFeeTypesResponse(feeTypes))
        .build();
  }

  private List<FeeDto.FeeTypesResponse> buildFeeTypesResponse(
      List<FeeGroupFeeType> feeGroupFeeType) {
    List<FeeDto.FeeTypesResponse> feeTypesResponses = new ArrayList<>();

    feeGroupFeeType.forEach(
        type -> {
          var createdBy = type.getCreatedBy();
          feeTypesResponses.add(
              FeeDto.FeeTypesResponse.builder()
                  .id(type.getId().toString())
                  .fineType(type.getFineType())
                  .userId(createdBy.getId())
                  .createdOn(convertIso8601ToEpoch(type.getCreatedAt().toLocalDateTime()))
                  .createdBy(createdBy.getFirstName() + " " + createdBy.getLastName())
                  .feeTypeId(type.getFeeType().getId().toString())
                  .dueDate(convertIso8601ToEpoch(type.getDueDate()))
                  .amount(type.getAmount())
                  .name(type.getFeeType().getName())
                  .fineAmount(type.getFineAmount())
                  .remainderDays(type.getRemainderDays())
                  .build());
        });

    return feeTypesResponses;
  }

  public void deleteFeeGroupFeeTypeById(String orgSlug, String feeTypeId, String feeGroupId) {
    var feeGroupFeeTypes = getFeeGroupFeeTypeById(feeTypeId, orgSlug, feeGroupId);
    feeGroupFeeTypeRepository.delete(feeGroupFeeTypes);
  }

  private FeeGroupFeeType getFeeGroupFeeTypeById(
      String feeTypeId, String orgSlug, String feeGroupId) {
    var feeGroupFeeType =
        feeGroupFeeTypeRepository.findByFeeGroupIdAndFeeTypeIdAndOrgSlug(
            UUID.fromString(feeGroupId), UUID.fromString(feeTypeId), orgSlug);
    if (feeGroupFeeType.isEmpty()) {
      throw new IllegalArgumentException("Fee group not found with id: " + feeTypeId);
    }
    return feeGroupFeeType.get();
  }

  public void updateFeeGroupFeeTypeById(
      String orgSlug,
      String feeGroupId,
      String feeTypeId,
      FeeDto.FeeGroupFeeTypeUpdateRequest request) {
    var feeGroupFeeType = getFeeGroupFeeTypeById(feeTypeId, orgSlug, feeGroupId);
    var feeType = getFeeTypeById(request.feeTypeId(), orgSlug);
    feeGroupFeeType.setFeeType(feeType);
    feeGroupFeeType.setFineType(request.fineType());
    feeGroupFeeType.setAmount(request.amount());
    feeGroupFeeType.setFineAmount(request.fineAmount());
    feeGroupFeeType.setFineType(request.fineType());
    feeGroupFeeType.setRemainderDays(request.remainderDays());

    feeGroupFeeTypeRepository.save(feeGroupFeeType);
  }

  public void saveFeeMaster(String orgSlug, FeeDto.FeeMasterRequest request) {
    var feeGroup = getFeeGroupById(request.feeGroupId(), orgSlug);
    var feeMaster =
        feeMasterRepository.save(
            FeeMaster.builder()
                .feeGroup(feeGroup)
                .orgSlug(orgSlug)
                .scopeType(request.scopeType())
                .rules(buildRules(request))
                .createdBy(authService.getUserDetails())
                .build());

    feeMasterEventPublisher.publishCreateFeeMaster(feeMaster.getId(), request, orgSlug);
  }

  private FeeDto.Rules buildRules(FeeDto.FeeMasterRequest request) {

    return switch (request.scopeType()) {
      case SECTION ->
          FeeDto.Rules.builder().paramType("sections").paramValue(request.sectionUuid()).build();
      case GRADE ->
          FeeDto.Rules.builder().paramType("grades").paramValue(request.gradeSlug()).build();
      case SCHOOL ->
          FeeDto.Rules.builder()
              .paramType("school")
              .paramValue(Collections.singletonList("school"))
              .build();
      case CUSTOM ->
          FeeDto.Rules.builder()
              .paramType("student_id")
              .paramValue(request.studentId().stream().map(String::valueOf).toList())
              .build();
    };
  }

  public List<FeeDto.FeeMasterResponse> getFeeMaster(String orgSlug) {
    return feeMasterRepository.findAllByOrgSlug(orgSlug).stream()
        .map(
            master ->
                FeeDto.FeeMasterResponse.builder()
                    .id(master.getId().toString())
                    .feeGroupId(master.getFeeGroup().getId().toString())
                    .feeGroupName(master.getFeeGroup().getName())
                    .feeGroupDescription(master.getFeeGroup().getDescription())
                    .scopeType(master.getScopeType())
                    .userId(master.getCreatedBy().getId())
                    .createdOn(convertIso8601ToEpoch(master.getCreatedAt().toLocalDateTime()))
                    .build())
        .toList();
  }

  public List<FeeDto.FeeHeadResponse> getFeeHeadsByFeeMaster(String orgSlug, String feeMasterId) {
    FeeMaster feeMaster = getFeeMasterById(feeMasterId, orgSlug);

    return feeHeadRepository.findAllByFeeMasterAndOrgSlug(feeMaster, orgSlug).stream()
        .map(
            feeHead -> {
              var student = feeHead.getStudent();
              var user = student.getUserInfo();
              var section = student.getSection();

              return FeeDto.FeeHeadResponse.builder()
                  .studentId(student.getId())
                  .studentName(user.getFirstName() + " " + user.getLastName())
                  .gradeSlug(section.getGradeSlug())
                  .gradeName(section.getGradeName())
                  .sectionUuid(section.getUuid().toString())
                  .sectionName(section.getName())
                  .build();
            })
        .distinct()
        .toList();
  }

  public FeeMaster getFeeMasterById(String feeMasterId, String orgSlug) {
    var feeMaster = feeMasterRepository.findByIdAndOrgSlug(UUID.fromString(feeMasterId), orgSlug);
    if (feeMaster.isEmpty()) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.FeeMaster.NotFound" + feeMaster);
    }
    return feeMaster.get();
  }

  public void publishFeeGroup(String orgSlug, String feeGroupId) {
    var feeGroup = getFeeGroupById(feeGroupId, orgSlug);
    if (feeGroup.getPublishedAt() != null) {
      throw new ApiException(
          InternalErrorCodes.INVALID_REQUEST, "error.FeeGroup.AlreadyPublished: " + feeGroupId);
    }
    feeGroup.setPublishedAt(LocalDateTime.now());
    feeGroupRepository.save(feeGroup);
  }
}
